import { Box } from '@chakra-ui/react';
import React, { useMemo, useEffect } from 'react';
import { CellMeasurerCache } from 'react-virtualized';

import {
  VirtualizedInfiniteTable,
  TableHeader,
} from 'components/update/Table/VirtualizedInfiniteTable';

import { useProdutosVinculacao } from '../hooks/useProdutosVinculacao';

import { ItemProduto } from './ItemProduto';
import { RodapeInformacoes } from './RodapeInformacoes';

interface ListagemProdutosProps {
  entradaMercadoriaId: string | null;
}

const cache = new CellMeasurerCache({
  defaultHeight: 65,
  minHeight: 52,
  fixedWidth: true,
});

export function ListagemProdutos({
  entradaMercadoriaId,
}: ListagemProdutosProps) {
  const {
    produtos,
    informacoesRodape,
    isCarregandoPagina,
    handleToggleLinhaProduto,
    handleEditar,
    handleVincularProduto,
    loadMoreRows,
  } = useProdutosVinculacao(entradaMercadoriaId);

  const produtosTableHeaders: TableHeader[] = useMemo(
    () => [
      {
        key: 'descricaoProduto',
        content: 'Produto',
        width: '70%',
        minWidth: '70%',
        paddingLeft: '48px !important',
      },
      {
        key: 'quantidade',
        content: 'Quantidade',
        isNumeric: false,
        width: '10%',
        minWidth: '10%',
        lineHeight: 'none',
        verticalAlign: 'bottom',
      },
      {
        key: 'valorUnitario',
        content: 'Valor unitário',
        width: '10%',
        minWidth: '10%',
        lineHeight: 'none',
        verticalAlign: 'bottom',
        isNumeric: true,
      },
      {
        key: 'valorTotal',
        content: 'Valor total',
        width: '10%',
        minWidth: '10%',
        lineHeight: 'none',
        verticalAlign: 'bottom',
        isNumeric: true,
      },
      {
        key: 'acoes',
        width: '180px',
        minWidth: '180px',
        content: 'Ações',
        textAlign: 'end',
        lineHeight: 'none',
        verticalAlign: 'bottom',
      },
    ],
    []
  );

  const heightTable = () => {
    const itemOpenInTable = produtos?.filter((produto) => produto.isOpen);
    const numberOfItensOpenInList = itemOpenInTable.length;
    const heightOfAllItensOpen = numberOfItensOpenInList * 112;
    const heightOfAllItensClosed =
      (produtos?.length - numberOfItensOpenInList) * 64;
    return heightOfAllItensOpen + heightOfAllItensClosed;
  };

  const getDynamicHeight = (index: number, marginSize: number) => {
    const produto = produtos[index];
    const isLastItem = index === produtos.length - 1;
    const closedProdutoHeight = 56 + (isLastItem ? 0 : marginSize);
    const openedProdutoHeight = 124 + (isLastItem ? 0 : marginSize);
    const produtoHeight = produto?.isOpen
      ? openedProdutoHeight
      : closedProdutoHeight;
    return produtoHeight;
  };

  // Bloquear scroll da tela durante carregamento
  useEffect(() => {
    if (isCarregandoPagina) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    // Cleanup para garantir que o scroll seja restaurado
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isCarregandoPagina]);

  return (
    <Box
      display="flex"
      flexDirection="column"
      justifyContent="space-between"
      borderRadius="md"
      border="1px"
      bg="gray.50"
      borderColor="gray.200"
      minH="390px"
      py={{ base: 4, sm: 6, md: 6 }}
      pl={{ base: 4, sm: 6, md: 6 }}
      pr={{ base: '6px', sm: '14px', md: '24px' }}
      sx={{
        '& table': { bg: 'gray.50' },
        '& thead > tr > th': {
          bg: 'gray.50',
          border: 'none',
        },
        '& td:first-of-type': {
          paddingLeft: '16px !important',
        },
        '& tbody > tr': {
          borderRadius: 'md',
          boxShadow: '0px 0px 2px #00000029',
          ...(informacoesRodape.totalProdutos > 0
            ? { border: '1px', borderColor: 'gray.100' }
            : {
                '& > td': {
                  position: 'relative',
                  _before: {
                    content: '""',
                    position: 'absolute',
                    h: 'full',
                    w: 'full',
                    top: 0,
                    left: 0,
                    borderLeft: 'none',
                    borderRight: 'none',
                    borderRadius: 'md',
                  },
                },
              }),
        },
        '& tbody > tr > td': {
          bg: 'white',
          lineHeight: 'none',
          _before: {
            border:
              informacoesRodape.totalProdutos > 0 ? 'none !important' : '1px',
            borderColor: 'gray.100',
          },
        },
      }}
    >
      <VirtualizedInfiniteTable
        variant="simple-card"
        size="sm"
        bg="gray.50"
        boxShadow="none"
        withoutRowsMessage="Nenhum produto adicionado."
        orderColumn="descricaoProduto"
        tableHeaders={produtosTableHeaders}
        itemHeight={54}
        visibleItemsCount={informacoesRodape?.quantidadeItens}
        dynamicHeight={({ index }) => getDynamicHeight(index, 5)}
        rowCount={informacoesRodape.totalProdutos}
        isRowLoaded={({ index }) => !!produtos[index]}
        loadMoreRows={loadMoreRows}
        heightTable={heightTable()}
        isLoading={isCarregandoPagina}
        rowRenderer={({ index, style, key, parent }) => {
          const produto = produtos[index];

          if (!produto) {
            return null;
          }

          return (
            <ItemProduto
              key={key}
              produto={produto}
              index={index}
              cache={cache}
              style={style}
              parent={parent}
              produtosTableHeaders={produtosTableHeaders}
              onToggle={handleToggleLinhaProduto}
              onEditar={handleEditar}
              onVincular={handleVincularProduto}
              getDynamicHeight={getDynamicHeight}
            />
          );
        }}
      />
      <RodapeInformacoes informacoesRodape={informacoesRodape} />
    </Box>
  );
}

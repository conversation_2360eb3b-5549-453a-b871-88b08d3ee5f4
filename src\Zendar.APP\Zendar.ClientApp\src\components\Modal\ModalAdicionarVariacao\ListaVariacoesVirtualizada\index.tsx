import { Box, BoxProps, Flex, Checkbox, Text } from '@chakra-ui/react';
import { useCallback, useRef } from 'react';
import {
  List,
  AutoSizer,
  CellMeasurerCache,
  CellMeasurer,
} from 'react-virtualized';

type VariacoesProps = {
  produtoCorTamanhoId: string;
  produto: string;
  cor: string;
  tamanho: string;
  imagem: string;
  isChecked: boolean;
};

type ListaVariacoesVirtualizadaProps = Omit<BoxProps, 'children'> & {
  listaVariacoes: VariacoesProps[];
  setListaVariacoes: (variacoes: VariacoesProps[]) => void;
  isAlterar?: boolean;
};

const ListaVariacoesVirtualizada = ({
  listaVariacoes,
  setListaVariacoes,
  isAlterar,
  ...rest
}: ListaVariacoesVirtualizadaProps) => {
  const listaVariacoesFiltrada = listaVariacoes.filter((item) => {
    if (item.cor === '' && item.tamanho === '') {
      return false;
    }
    return true;
  });
  const todasSelecionadas = listaVariacoesFiltrada?.every(
    ({ isChecked }) => isChecked
  );

  const cache = useRef(
    new CellMeasurerCache({
      fixedWidth: true,
      defaultHeight: 40,
    })
  );

  const toggleSelecionarTodos = useCallback(
    (value: boolean) => {
      const novaListaVariacoes = listaVariacoesFiltrada?.map((variacao) => ({
        ...variacao,
        isChecked: value,
      }));

      setListaVariacoes(novaListaVariacoes);
    },
    [listaVariacoesFiltrada, setListaVariacoes]
  );

  const toggleSelecionarVariacao = useCallback(
    (index: number) => {
      const novaListaVariacoes = listaVariacoesFiltrada?.map((variacoes, i) => {
        if (i === index) {
          return {
            ...variacoes,
            isChecked: !variacoes.isChecked,
          };
        }
        return variacoes;
      });

      setListaVariacoes(novaListaVariacoes);
    },
    [listaVariacoesFiltrada, setListaVariacoes]
  );

  return (
    <Box
      w="full"
      h="350px"
      borderRadius="5px"
      sx={{
        '& .list-component::-webkit-scrollbar-thumb': {
          background: 'gray.200',
        },
        '& .list-component::-webkit-scrollbar-thumb:hover': {
          background: 'gray.200',
        },
      }}
      {...rest}
    >
      {!isAlterar && (
        <Flex
          h="40px"
          mb="4px"
          px="16px"
          gap="16px"
          w="full"
          borderRadius="5px"
          borderWidth="1px"
          cursor="pointer"
          userSelect="none"
          bg={todasSelecionadas ? 'purple.50' : 'white'}
          borderColor="gray.200"
          fontSize="14px"
          align="center"
          onClick={() => toggleSelecionarTodos(!todasSelecionadas)}
        >
          <Checkbox
            mb="0"
            colorScheme="purple"
            isChecked={todasSelecionadas}
            pointerEvents="none"
          />
          <Text>Selecionar todas</Text>
        </Flex>
      )}
      <Box h={['330px', '440px']} maxH="320px">
        <AutoSizer>
          {({ width, height }) => (
            <List
              className="list-component"
              width={width}
              height={height}
              tabIndex={-1}
              rowHeight={cache.current.rowHeight}
              deferredMeasurementCache={cache.current}
              rowCount={listaVariacoesFiltrada?.length}
              rowRenderer={({ index, key, parent, style }) => {
                const variacao = listaVariacoesFiltrada[index];

                const { isChecked, cor, tamanho } = variacao;

                const labelCor = cor ? `${cor} ${tamanho ? '|' : ''}` : '';

                return (
                  <CellMeasurer
                    key={key}
                    cache={cache.current}
                    parent={parent}
                    columnIndex={0}
                    rowIndex={index}
                  >
                    <Box style={style}>
                      <Flex
                        h="40px"
                        mb="4px"
                        px="16px"
                        gap="16px"
                        borderRadius="5px"
                        align="center"
                        borderWidth="1px"
                        fontSize="14px"
                        userSelect="none"
                        bg={isChecked ? 'purple.50' : 'white'}
                        borderColor="gray.200"
                        pointerEvents={isAlterar ? 'none' : 'auto'}
                        onClick={() => toggleSelecionarVariacao(index)}
                      >
                        <Checkbox
                          m="0"
                          colorScheme="purple"
                          isChecked={variacao.isChecked}
                          pointerEvents="none"
                        />
                        <Flex gap="4px">
                          <Text color="teal.600">{labelCor}</Text>
                          <Text color="pink.700">{tamanho}</Text>
                        </Flex>
                      </Flex>
                    </Box>
                  </CellMeasurer>
                );
              }}
            />
          )}
        </AutoSizer>
      </Box>
    </Box>
  );
};

export default ListaVariacoesVirtualizada;

import { Box, Flex, Text, Divider, useBreakpointValue } from '@chakra-ui/react';
import React from 'react';

import { TextValor } from 'components/PDV/Text/TextValor';

interface TotalizadoresFixosProps {
  quantidadeItens: number;
  totalProdutos: number;
  valorTotalProdutos: number;
}

const TotalizadoresFixos: React.FC<TotalizadoresFixosProps> = ({
  quantidadeItens,
  totalProdutos,
  valorTotalProdutos,
}) => {
  const isResponsiveVersion = useBreakpointValue({
    base: true,
    md: false,
    lg: false,
  });

  return (
    <Box
      position="fixed"
      bottom="70px"
      left="0"
      right="0"
      bg="white"
      boxShadow="0 -4px 12px rgba(0, 0, 0, 0.1)"
      zIndex={10}
      h="52px"
    >
      <Flex direction={{ base: 'column', md: 'row' }} h="full">
        <Flex
          direction={{ base: 'column', md: 'row', lg: 'row' }}
          justify="center"
          w="full"
          gap={['24px', '24px', '12px', '24px']}
          align="center"
        >
          <Flex
            alignItems="center"
            gap="4px"
            justifyContent="center"
            direction={{
              base: 'column',
              md: 'column',
              lg: 'column',
              xl: 'row',
            }}
          >
            <Text
              color="black"
              fontSize="sm"
              mr={{ base: '0', md: '1', lg: '1' }}
              mb={{ base: '1', md: '0', lg: '0' }}
              fontWeight="medium"
            >
              Total de produtos:
            </Text>
            <Text
              fontWeight="semibold"
              color="black"
              fontSize="xl"
              lineHeight="none"
            >
              {totalProdutos.toLocaleString('pt-BR')}
            </Text>
          </Flex>

          <Divider
            color="gray.700"
            orientation={!isResponsiveVersion ? 'vertical' : 'horizontal'}
            h={{ base: '1px', md: '12', lg: '12' }}
            w={{ base: 'full', md: '1', lg: '1' }}
          />

          <Flex
            alignItems="center"
            justifyContent="center"
            gap="4px"
            direction={{
              base: 'column',
              md: 'column',
              lg: 'column',
              xl: 'row',
            }}
          >
            <Text
              color="black"
              fontSize="sm"
              fontWeight="medium"
              mr={{ base: '0', md: '1', lg: '1' }}
              mb={{ base: '1', md: '0', lg: '0' }}
            >
              Quantidade de itens:
            </Text>
            <Text
              fontWeight="semibold"
              color="black"
              fontSize="xl"
              lineHeight="none"
            >
              {quantidadeItens.toLocaleString('pt-BR')}
            </Text>
          </Flex>

          <Divider
            color="gray.700"
            orientation={!isResponsiveVersion ? 'vertical' : 'horizontal'}
            h={{ base: '1px', md: '12', lg: '12' }}
            w={{ base: 'full', md: '1', lg: '1' }}
          />

          <Flex
            gap="4px"
            alignItems="center"
            justifyContent="center"
            direction={{
              base: 'column',
              md: 'column',
              lg: 'column',
              xl: 'row',
            }}
          >
            <Text
              color="black"
              fontSize="sm"
              fontWeight="medium"
              mr={{ base: '0', md: '1', lg: '1' }}
              mb={{ base: '1', md: '0', lg: '0' }}
            >
              Total da entrada:
            </Text>
            <TextValor
              casasDecimais={2}
              valor={valorTotalProdutos}
              color="black"
              fontSize="xl"
              fontWeight="semibold"
              symbolFontSize="xs"
              symbolFontWeight="semibold"
            />
          </Flex>
        </Flex>
      </Flex>
    </Box>
  );
};

export default TotalizadoresFixos;
